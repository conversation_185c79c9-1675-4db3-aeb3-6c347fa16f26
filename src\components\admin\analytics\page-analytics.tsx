'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createClient } from '@/lib/supabase/client'
import { useTranslations } from 'next-intl'
import { FileText, Eye, Filter, Globe, TrendingUp, ExternalLink, Share2 } from 'lucide-react'
import { EXCLUDED_EMAILS } from '@/lib/analytics/analytics-service'

interface PageData {
  path: string
  title: string
  visits: number
  uniqueVisitors: number
  percentage: number
  avgDuration: number
}

interface PageAnalyticsProps {
  className?: string
  showTopCount?: number
}

// Helper function to categorize traffic sources
const categorizeTrafficSource = (referrer: string): string => {
  if (!referrer || referrer === '') return 'direct'

  const url = referrer.toLowerCase()

  // Search engines
  if (url.includes('google.') || url.includes('bing.') || url.includes('yahoo.') ||
      url.includes('duckduckgo.') || url.includes('baidu.') || url.includes('yandex.')) {
    return 'search'
  }

  // Social media
  if (url.includes('facebook.') || url.includes('instagram.') || url.includes('twitter.') ||
      url.includes('linkedin.') || url.includes('pinterest.') || url.includes('tiktok.') ||
      url.includes('youtube.') || url.includes('whatsapp.') || url.includes('telegram.')) {
    return 'social'
  }

  // Email
  if (url.includes('mail.') || url.includes('gmail.') || url.includes('outlook.') ||
      url.includes('yahoo.') || url.includes('webmail.') || url.includes('email.')) {
    return 'email'
  }

  // Same domain (internal)
  if (url.includes('primecaffe.ch') || url.includes('localhost')) {
    return 'internal'
  }

  // Everything else is referral
  return 'referral'
}

// Page title mapping for better display
const getPageDisplayName = (path: string, title: string): string => {
  if (title && title !== path && !title.includes('PrimeCaffe')) {
    return title
  }

  // Map common paths to readable names
  const pathMap: Record<string, string> = {
    '/': 'Homepage',
    '/de': 'Homepage (DE)',
    '/it': 'Homepage (IT)',
    '/fr': 'Homepage (FR)',
    '/shop': 'Shop',
    '/de/shop': 'Shop (DE)',
    '/it/shop': 'Shop (IT)',
    '/fr/shop': 'Shop (FR)',
    '/builder': 'Coffee Box Builder',
    '/de/builder': 'Coffee Box Builder (DE)',
    '/it/builder': 'Coffee Box Builder (IT)',
    '/fr/builder': 'Coffee Box Builder (FR)',
    '/bundles': 'Bundles',
    '/de/bundles': 'Bundles (DE)',
    '/it/bundles': 'Bundles (IT)',
    '/fr/bundles': 'Bundles (FR)',
    '/about': 'About Us',
    '/de/about': 'About Us (DE)',
    '/it/about': 'About Us (IT)',
    '/fr/about': 'About Us (FR)',
    '/contact': 'Contact',
    '/de/contact': 'Contact (DE)',
    '/it/contact': 'Contact (IT)',
    '/fr/contact': 'Contact (FR)',
    '/account': 'Account',
    '/de/account': 'Account (DE)',
    '/it/account': 'Account (IT)',
    '/fr/account': 'Account (FR)',
    '/login': 'Login',
    '/register': 'Register'
  }

  return pathMap[path] || path
}

const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}m ${remainingSeconds}s`
}

export function PageAnalytics({ className = '', showTopCount = 20 }: PageAnalyticsProps) {
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])
  const [loading, setLoading] = useState(true)
  const [pageData, setPageData] = useState<PageData[]>([])
  const [availableLanguages, setAvailableLanguages] = useState<string[]>([])
  const [selectedLanguage, setSelectedLanguage] = useState<string>('all')
  const [selectedTrafficSource, setSelectedTrafficSource] = useState<string>('all')
  const [totalVisits, setTotalVisits] = useState(0)

  useEffect(() => {
    const loadPageAnalytics = async () => {
      try {
        setLoading(true)

        // Get excluded admin/dev user IDs
        const { data: excludedUsers } = await supabase
          .from('users')
          .select('id')
          .in('email', EXCLUDED_EMAILS)

        const excludedUserIds = excludedUsers?.map(u => u.id) || []
        console.log('🔍 Debug: Excluded user IDs:', excludedUserIds)

        // Calculate current month date range
        const now = new Date()
        const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1)

        // Get available languages first (simple query without admin filtering for now)
        const { data: languageData, error: languageError } = await supabase
          .from('page_visits')
          .select('browser_language')
          .gte('created_at', currentMonth.toISOString())

        if (languageError) {
          console.error('Error loading language data:', languageError)
        }

        const languages = [...new Set(languageData?.map(d => d.browser_language).filter(Boolean))] as string[]
        setAvailableLanguages(languages.sort())

        // Get all page visits
        let query = supabase
          .from('page_visits')
          .select('*')
          .gte('created_at', currentMonth.toISOString())

        if (selectedLanguage !== 'all') {
          query = query.eq('browser_language', selectedLanguage)
        }

        const { data: allVisits, error: visitsError } = await query

        if (visitsError) {
          console.error('Error loading page visits:', visitsError)
          setPageData([])
          setTotalVisits(0)
          return
        }

        // Get all sessions to filter admin users
        const { data: allSessions, error: sessionsError } = await supabase
          .from('visitor_sessions')
          .select('session_id, user_id')

        if (sessionsError) {
          console.error('Error loading sessions:', sessionsError)
        }

        // Create a map of session_id to user_id for quick lookup
        const sessionUserMap = new Map<string, string | null>()
        allSessions?.forEach(session => {
          sessionUserMap.set(session.session_id, session.user_id)
        })

        // Filter out admin users from visits
        console.log('🔍 Debug: Total visits before filtering:', allVisits?.length)
        console.log('🔍 Debug: Session user map size:', sessionUserMap.size)

        const filteredVisits = allVisits?.filter(visit => {
          const userId = sessionUserMap.get(visit.session_id)
          const isAdmin = userId && excludedUserIds.includes(userId)

          if (visit.page_path.includes('admin')) {
            console.log('🔍 Debug admin page:', {
              page: visit.page_path,
              sessionId: visit.session_id,
              userId,
              isAdmin,
              shouldKeep: !isAdmin
            })
          }

          // Keep visits from anonymous sessions (user_id is null) or non-admin users
          return !userId || !excludedUserIds.includes(userId)
        }) || []

        console.log('🔍 Debug: Total visits after filtering:', filteredVisits?.length)

        // Filter by traffic source if selected
        let visits = filteredVisits
        if (selectedTrafficSource !== 'all' && filteredVisits) {
          visits = filteredVisits.filter(visit =>
            categorizeTrafficSource(visit.referrer || '') === selectedTrafficSource
          )
        }

        if (!visits || visits.length === 0) {
          setPageData([])
          setTotalVisits(0)
          return
        }

        // Group by page path and calculate metrics
        const pageStats: Record<string, {
          visits: number
          uniqueVisitors: Set<string>
          totalDuration: number
          title: string
        }> = {}

        visits.forEach(visit => {
          const path = visit.page_path
          if (!pageStats[path]) {
            pageStats[path] = {
              visits: 0,
              uniqueVisitors: new Set(),
              totalDuration: 0,
              title: visit.page_title || path
            }
          }

          pageStats[path].visits += 1
          pageStats[path].uniqueVisitors.add(visit.session_id)
          pageStats[path].totalDuration += visit.visit_duration_seconds || 0
        })

        const total = visits.length
        setTotalVisits(total)

        // Convert to array and sort by visits
        const pageArray = Object.entries(pageStats)
          .map(([path, stats]) => ({
            path,
            title: getPageDisplayName(path, stats.title),
            visits: stats.visits,
            uniqueVisitors: stats.uniqueVisitors.size,
            percentage: total > 0 ? (stats.visits / total) * 100 : 0,
            avgDuration: stats.visits > 0 ? Math.round(stats.totalDuration / stats.visits) : 0
          }))
          .sort((a, b) => b.visits - a.visits)
          .slice(0, showTopCount)

        setPageData(pageArray)
      } catch (error) {
        console.error('Error loading page analytics:', error)
        setPageData([])
        setTotalVisits(0)
      } finally {
        setLoading(false)
      }
    }

    loadPageAnalytics()
  }, [supabase, selectedLanguage, selectedTrafficSource, showTopCount])

  const getLanguageDisplayName = (langCode: string): string => {
    const langMap: Record<string, string> = {
      'de': 'Deutsch',
      'de-DE': 'Deutsch',
      'de-CH': 'Deutsch (CH)',
      'it': 'Italiano',
      'it-IT': 'Italiano',
      'it-CH': 'Italiano (CH)',
      'fr': 'Français',
      'fr-FR': 'Français',
      'fr-CH': 'Français (CH)',
      'en': 'English',
      'en-US': 'English (US)',
      'en-GB': 'English (UK)'
    }
    return langMap[langCode] || langCode
  }

  const getTrafficSourceDisplayName = (source: string): string => {
    const sourceMap: Record<string, { name: string; icon: string }> = {
      'direct': { name: t('analytics.trafficSource.direct') || 'Diretto', icon: '🔗' },
      'search': { name: t('analytics.trafficSource.search') || 'Motori di Ricerca', icon: '🔍' },
      'social': { name: t('analytics.trafficSource.social') || 'Social Media', icon: '📱' },
      'email': { name: t('analytics.trafficSource.email') || 'Email', icon: '📧' },
      'referral': { name: t('analytics.trafficSource.referral') || 'Referral', icon: '🌐' },
      'internal': { name: t('analytics.trafficSource.internal') || 'Interno', icon: '🏠' }
    }
    return sourceMap[source]?.name || source
  }

  const getTrafficSourceIcon = (source: string): string => {
    const sourceMap: Record<string, string> = {
      'direct': '🔗',
      'search': '🔍',
      'social': '📱',
      'email': '📧',
      'referral': '🌐',
      'internal': '🏠'
    }
    return sourceMap[source] || '🌐'
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            <div className="animate-pulse h-5 bg-gray-200 rounded w-48"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="animate-pulse h-10 bg-gray-200 rounded"></div>
            {[...Array(8)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex justify-between items-center p-3 border rounded-lg">
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {t('analytics.topPages') || 'Pagine Più Visitate'}
        </CardTitle>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Eye className="h-4 w-4" />
            <span>
              {totalVisits.toLocaleString()} {t('analytics.totalPageViews') || 'visualizzazioni totali'}
            </span>
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            <Filter className="h-4 w-4" />
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder={t('analytics.filterByLanguage') || 'Filtra per lingua'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    {t('analytics.allLanguages') || 'Tutte le lingue'}
                  </div>
                </SelectItem>
                {availableLanguages.map(lang => (
                  <SelectItem key={lang} value={lang}>
                    {getLanguageDisplayName(lang)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedTrafficSource} onValueChange={setSelectedTrafficSource}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder={t('analytics.filterByTrafficSource') || 'Filtra per sorgente'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <div className="flex items-center gap-2">
                    <Share2 className="h-4 w-4" />
                    {t('analytics.allTrafficSources') || 'Tutte le sorgenti'}
                  </div>
                </SelectItem>
                {['direct', 'search', 'social', 'referral', 'email'].map(source => (
                  <SelectItem key={source} value={source}>
                    <div className="flex items-center gap-2">
                      <span>{getTrafficSourceIcon(source)}</span>
                      {getTrafficSourceDisplayName(source)}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {pageData.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t('analytics.noPageData') || 'Nessun dato pagina disponibile'}</p>
          </div>
        ) : (
          <div className="space-y-3">
            {pageData.map((page, index) => (
              <div key={page.path} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{page.title}</span>
                    {index === 0 && (
                      <Badge variant="secondary" className="text-xs">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {t('analytics.topPage') || 'Top'}
                      </Badge>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => window.open(page.path, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{page.path}</span>
                    <span>•</span>
                    <span>{page.uniqueVisitors} {t('analytics.uniqueVisitors') || 'visitatori unici'}</span>
                    <span>•</span>
                    <span>{formatDuration(page.avgDuration)} {t('analytics.avgTime') || 'tempo medio'}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-lg">
                    {page.visits.toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {page.percentage.toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
