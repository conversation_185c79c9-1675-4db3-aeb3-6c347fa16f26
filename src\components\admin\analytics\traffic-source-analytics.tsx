'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { createClient } from '@/lib/supabase/client'
import { useTranslations } from 'next-intl'
import { Share2, TrendingUp, Users } from 'lucide-react'

interface TrafficSourceData {
  source: string
  visitors: number
  percentage: number
  displayName: string
  icon: string
}

interface TrafficSourceAnalyticsProps {
  className?: string
  showTopCount?: number
}

// Helper function to categorize traffic sources
const categorizeTrafficSource = (referrer: string): string => {
  if (!referrer || referrer === '') return 'direct'
  
  const url = referrer.toLowerCase()
  
  // Search engines
  if (url.includes('google.') || url.includes('bing.') || url.includes('yahoo.') || 
      url.includes('duckduckgo.') || url.includes('baidu.') || url.includes('yandex.')) {
    return 'search'
  }
  
  // Social media
  if (url.includes('facebook.') || url.includes('instagram.') || url.includes('twitter.') || 
      url.includes('linkedin.') || url.includes('pinterest.') || url.includes('tiktok.') ||
      url.includes('youtube.') || url.includes('whatsapp.') || url.includes('telegram.')) {
    return 'social'
  }
  
  // Email
  if (url.includes('mail.') || url.includes('gmail.') || url.includes('outlook.') || 
      url.includes('yahoo.') || url.includes('webmail.') || url.includes('email.')) {
    return 'email'
  }
  
  // Same domain (internal)
  if (url.includes('primecaffe.ch') || url.includes('localhost')) {
    return 'internal'
  }
  
  // Everything else is referral
  return 'referral'
}

// Helper function to get traffic source info
const getTrafficSourceInfo = (source: string, t: (key: string) => string): { name: string; icon: string } => {
  const sourceMap: Record<string, { name: string; icon: string }> = {
    'direct': { name: t('analytics.trafficSource.direct') || 'Diretto', icon: '🔗' },
    'search': { name: t('analytics.trafficSource.search') || 'Motori di Ricerca', icon: '🔍' },
    'social': { name: t('analytics.trafficSource.social') || 'Social Media', icon: '📱' },
    'email': { name: t('analytics.trafficSource.email') || 'Email', icon: '📧' },
    'referral': { name: t('analytics.trafficSource.referral') || 'Referral', icon: '🌐' },
    'internal': { name: t('analytics.trafficSource.internal') || 'Interno', icon: '🏠' }
  }
  return sourceMap[source] || { name: source, icon: '🌐' }
}

export function TrafficSourceAnalytics({ className = '', showTopCount = 6 }: TrafficSourceAnalyticsProps) {
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])
  const [loading, setLoading] = useState(true)
  const [trafficData, setTrafficData] = useState<TrafficSourceData[]>([])
  const [totalVisitors, setTotalVisitors] = useState(0)

  useEffect(() => {
    const loadTrafficSourceAnalytics = async () => {
      try {
        setLoading(true)

        // Get excluded user IDs
        const { data: excludedUsers } = await supabase
          .from('users')
          .select('id')
          .in('email', ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'])
        
        const excludedUserIds = excludedUsers?.map(u => u.id) || []

        // Calculate current month date range
        const now = new Date()
        const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1)

        // Get current month visitor sessions (excluding admin users)
        let sessionsQuery = supabase
          .from('visitor_sessions')
          .select('referrer')
          .gte('first_visit_at', currentMonth.toISOString())

        // Filter out admin users, but keep anonymous sessions (user_id is null)
        if (excludedUserIds.length > 0) {
          sessionsQuery = sessionsQuery.or(`user_id.is.null,user_id.not.in.(${excludedUserIds.join(',')})`)
        }

        const { data: sessions } = await sessionsQuery

        if (!sessions) {
          setTrafficData([])
          setTotalVisitors(0)
          return
        }

        // Categorize traffic sources (exclude internal traffic)
        const sourceCounts: Record<string, number> = {}
        sessions.forEach(session => {
          const source = categorizeTrafficSource(session.referrer || '')
          // Skip internal traffic - we only want to show how users arrive at the site
          if (source !== 'internal') {
            sourceCounts[source] = (sourceCounts[source] || 0) + 1
          }
        })

        const total = sessions.length
        setTotalVisitors(total)

        // Convert to array and sort by count
        const trafficArray = Object.entries(sourceCounts)
          .map(([source, visitors]) => {
            const sourceInfo = getTrafficSourceInfo(source, t)
            return {
              source,
              visitors,
              percentage: total > 0 ? (visitors / total) * 100 : 0,
              displayName: sourceInfo.name,
              icon: sourceInfo.icon
            }
          })
          .sort((a, b) => b.visitors - a.visitors)
          .slice(0, showTopCount)

        setTrafficData(trafficArray)
      } catch (error) {
        console.error('Error loading traffic source analytics:', error)
        setTrafficData([])
        setTotalVisitors(0)
      } finally {
        setLoading(false)
      }
    }

    loadTrafficSourceAnalytics()
  }, [supabase, showTopCount, t])

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            <div className="animate-pulse h-5 bg-gray-200 rounded w-48"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex justify-between items-center mb-2">
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="h-2 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Share2 className="h-5 w-5" />
          {t('analytics.trafficSources') || 'Sorgenti di Traffico'}
        </CardTitle>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Users className="h-4 w-4" />
          <span>
            {totalVisitors.toLocaleString()} {t('analytics.monthlyVisitors') || 'visitatori mensili'}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        {trafficData.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Share2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t('analytics.noTrafficData') || 'Nessun dato traffico disponibile'}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {trafficData.map((traffic, index) => (
              <div key={traffic.source} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{traffic.icon}</span>
                    <span className="font-medium">{traffic.displayName}</span>
                    {index === 0 && (
                      <Badge variant="secondary" className="text-xs">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {t('analytics.topSource') || 'Top'}
                      </Badge>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">
                      {traffic.visitors.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {traffic.percentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
                <Progress 
                  value={traffic.percentage} 
                  className="h-2"
                />
              </div>
            ))}
          </div>
        )}
        
        {trafficData.length > 0 && (
          <div className="mt-6 pt-4 border-t">
            <div className="text-sm text-muted-foreground">
              <p className="mb-2">
                <strong>{t('analytics.insights') || 'Insights'}:</strong>
              </p>
              <ul className="space-y-1 text-xs">
                {trafficData[0] && (
                  <li>
                    • {trafficData[0].displayName} è la principale sorgente di traffico ({trafficData[0].percentage.toFixed(1)}%)
                  </li>
                )}
                <li>
                  • {trafficData.length} sorgenti di traffico rilevate questo mese
                </li>
                <li>
                  • {totalVisitors.toLocaleString()} visitatori totali analizzati
                </li>
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
